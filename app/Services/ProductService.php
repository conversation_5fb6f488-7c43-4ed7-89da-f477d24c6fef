<?php

namespace App\Services;

use App\Models\Product;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Support\Str;

class ProductService
{
    /**
     * Find similar products based on category and name similarity
     *
     * @param Product $product
     * @return Collection
     */
    public function findSimilarProducts(Product $product): Collection
    {
        if (!$product->category_id) {
            return collect();
        }

        // Get all products in the same category (excluding current product)
        $candidateProducts = Product::where('category_id', $product->category_id)
            ->where('id', '!=', $product->id)
            ->where('stock', '>', 0) // Only show products in stock
            ->get();

        // Extract base name from the current product
        $baseProductName = $this->extractBaseName($product->name);

        // Filter products with similar base names
        $similarProducts = $candidateProducts->filter(function ($candidateProduct) use ($baseProductName) {
            $candidateBaseName = $this->extractBaseName($candidateProduct->name);
            return $this->areBaseNamesSimilar($baseProductName, $candidateBaseName);
        });

        return $similarProducts;
    }

    /**
     * Extract base name by removing trailing variations (weight, color, size, etc.)
     *
     * @param string $productName
     * @return string
     */
    protected function extractBaseName(string $productName): string
    {
        // Normalize the name
        $name = trim($productName);

        // Common patterns to remove (weight, size, color variations)
        $patterns = [
            // Weight patterns: 17kg, 11,4kg, 2.5kg, etc.
            '/\s+\d+[,.]?\d*\s*kg$/i',
            '/\s+\d+[,.]?\d*\s*g$/i',
            '/\s+\d+[,.]?\d*\s*l$/i',
            '/\s+\d+[,.]?\d*\s*ml$/i',
            
            // Size patterns: S, M, L, XL, XXL, etc.
            '/\s+[SMLX]{1,3}$/i',
            '/\s+(small|medium|large|extra\s*large)$/i',
            
            // Color patterns (common Polish colors)
            '/\s+(czarny|czarne|biały|białe|czerwony|czerwone|niebieski|niebieskie|zielony|zielone|żółty|żółte|różowy|różowe|szary|szare|brązowy|brązowe)$/i',
            
            // Size with color combinations: "M czarne", "S czerwone", etc.
            '/\s+[SMLX]{1,3}\s+(czarny|czarne|biały|białe|czerwony|czerwone|niebieski|niebieskie|zielony|zielone|żółty|żółte|różowy|różowe|szary|szare|brązowy|brązowe)$/i',
            
            // Numeric sizes: 38, 42, etc.
            '/\s+\d{1,3}$/i',
            
            // Pack sizes: "2szt", "5 sztuk", etc.
            '/\s+\d+\s*szt\.?$/i',
            '/\s+\d+\s*sztuk$/i',
            '/\s+\d+\s*pack$/i',
            
            // Volume patterns with units
            '/\s+\d+[,.]?\d*\s*(ml|l|cm|mm|m)$/i',
        ];

        foreach ($patterns as $pattern) {
            $name = preg_replace($pattern, '', $name);
        }

        return trim($name);
    }

    /**
     * Check if two base names are similar enough to be considered variants
     *
     * @param string $baseName1
     * @param string $baseName2
     * @return bool
     */
    protected function areBaseNamesSimilar(string $baseName1, string $baseName2): bool
    {
        // Exact match
        if (strcasecmp($baseName1, $baseName2) === 0) {
            return true;
        }

        // Normalize for comparison
        $name1 = $this->normalizeForComparison($baseName1);
        $name2 = $this->normalizeForComparison($baseName2);

        // Check if one is contained in the other (with some tolerance)
        $similarity = similar_text($name1, $name2, $percent);
        
        // Consider similar if 85% or more similarity
        return $percent >= 85;
    }

    /**
     * Normalize string for comparison
     *
     * @param string $name
     * @return string
     */
    protected function normalizeForComparison(string $name): string
    {
        // Convert to lowercase
        $name = mb_strtolower($name, 'UTF-8');
        
        // Remove extra spaces
        $name = preg_replace('/\s+/', ' ', $name);
        
        // Remove common words that don't affect similarity
        $commonWords = ['dla', 'z', 'w', 'na', 'do', 'od', 'ze', 'i', 'a', 'the', 'for', 'with', 'and'];
        foreach ($commonWords as $word) {
            $name = preg_replace('/\b' . preg_quote($word, '/') . '\b/', '', $name);
        }
        
        // Clean up extra spaces again
        $name = preg_replace('/\s+/', ' ', $name);
        
        return trim($name);
    }

    /**
     * Extract the difference between product name and base name
     * This shows what makes this variant unique (weight, color, size, etc.)
     *
     * @param string $fullName
     * @param string $baseName
     * @return string
     */
    public function extractVariantDifference(string $fullName, string $baseName): string
    {
        // Remove base name from full name to get the difference
        $difference = trim(str_ireplace($baseName, '', $fullName));
        
        // Clean up any leading/trailing separators
        $difference = trim($difference, ' -,');
        
        // If no clear difference found, try to extract common variant patterns
        if (empty($difference)) {
            $patterns = [
                '/(\d+[,.]?\d*\s*(?:kg|g|l|ml))$/i',
                '/([SMLX]{1,3})$/i',
                '/(\d{1,3})$/i',
                '/(czarny|czarne|biały|białe|czerwony|czerwone|niebieski|niebieskie|zielony|zielone|żółty|żółte|różowy|różowe|szary|szare|brązowy|brązowe)$/i',
            ];
            
            foreach ($patterns as $pattern) {
                if (preg_match($pattern, $fullName, $matches)) {
                    $difference = $matches[1];
                    break;
                }
            }
        }
        
        return $difference ?: 'Standard';
    }

    /**
     * Get variant differences for a collection of similar products
     *
     * @param Product $mainProduct
     * @param Collection $similarProducts
     * @return array
     */
    public function getVariantDifferences(Product $mainProduct, Collection $similarProducts): array
    {
        $baseName = $this->extractBaseName($mainProduct->name);
        $variants = [];

        // Add main product
        $variants[] = [
            'product' => $mainProduct,
            'difference' => $this->extractVariantDifference($mainProduct->name, $baseName),
            'is_current' => true,
        ];

        // Add similar products
        foreach ($similarProducts as $product) {
            $variants[] = [
                'product' => $product,
                'difference' => $this->extractVariantDifference($product->name, $baseName),
                'is_current' => false,
            ];
        }

        return $variants;
    }
}
