<?php

namespace App\Services;

use App\Models\Product;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

class MolosProducts extends MolosApiService
{
    public function getProducts($page = 1, $limit = 100)
    {
        $response = $this->getHttpClient()->get($this->apiUrl . '/products/details', [
            'page' => $page,
            'limit' => $limit,
            'status_id' => 4,
        ]);

        if ($response->successful()) {
            $result = $response->json();
            $products = $result['products'];
            $total = $result['total'];
            $next = ($page * $limit) < $total;

            return [$products, $next];
        } else {
            Log::error('Failed to fetch products from Molos API', ['page' => $page, 'limit' => $limit]);
            return [[], false];
        }
    }


    public function importProducts($page = 1, $limit = 50)
    {
        list($products, $next) = $this->getProducts($page, $limit);

        if (empty($products)) {
            Log::info('No products to process on page ' . $page);
            return false;
        }

        foreach ($products as $productData) {
            try {
                // Check by molos_id first, then by SKU as fallback
                $existingProduct = null;
                if (!empty($productData['id'])) {
                    $existingProduct = Product::where('molos_id', $productData['id'])->first();
                }
                if (!$existingProduct && !empty($productData['symbol'])) {
                    $existingProduct = Product::where('sku', $productData['symbol'])->first();
                }

                if (!$existingProduct) {
                    $product = $this->importOneProduct($productData);
                    if ($product) {
                        Log::info('Successfully imported product', ['sku' => $product->sku, 'molos_id' => $product->molos_id]);
                    }
                } else {
                    $updatedProduct = $this->updateExistingProduct($existingProduct, $productData);
                    if ($updatedProduct) {
                        Log::info('Successfully updated product', ['sku' => $updatedProduct->sku, 'molos_id' => $updatedProduct->molos_id]);
                    }
                }
            } catch (\Exception $e) {
                Log::error('Failed to import product', [
                    'sku' => $productData['symbol'] ?? 'unknown',
                    'molos_id' => $productData['id'] ?? 'unknown',
                    'error' => $e->getMessage()
                ]);
                continue; // Skip to next product on error
            }
        }

        return $next;
    }

    public function importOneProduct($productData)
    {
        try {
            // Log::info('Importing product', ['product' => $productData]);
            // Handle category
            $category = null;
            if (!empty($productData['category_id'])) {
                $category = (new MolosCategories)->handleCategory($productData['category_id']);
                if (!$category) {
                    Log::warning('Category not found for product', [
                        'sku' => $productData['symbol'],
                        'category_id' => $productData['category_id']
                    ]);
                    return null; // Skip this product if category is required
                }
            }

            // Handle molos category
            $molosCategoryId = null;
            if (!empty($productData['category_id'])) {
                $molosCategory = (new MolosCategories)->handleMolosCategory($productData['category_id']);
                if ($molosCategory) {
                    $molosCategoryId = $molosCategory->molos_id;
                }
            }

            // Handle producent
            $producent = (new MolosProducents)->handleProducent($productData['producer_id']);
            if (!$producent) {
                Log::warning('Producent not found for product', [
                    'sku' => $productData['symbol'],
                    'producer_id' => $productData['producer_id']
                ]);
                return null;
            }

            // Calculate price with markup
            $molosPrice = $productData['price_gross'] ?? 0;
            $defaultPercentage = $category && $category->price_percentage !== null ? $category->price_percentage : 25;
            $finalPrice = round($molosPrice * (1 + ($defaultPercentage / 100)), 2);

            // Handle gallery
            $gallery = $this->handleGallery($productData['gallery'] ?? []);

            // Create product
            $product = Product::create([
                'sku' => $productData['symbol'],
                'molos_id' => $productData['id'] ?? null,
                'name' => $productData['name'],
                'description' => $productData['desc_long'] ?? null,
                'short_description' => $productData['desc_short'] ?? null,
                'price' => $finalPrice,
                'molos_price' => $molosPrice,
                'stock' => $productData['store'] ?? 0,
                'weight' => $productData['weight'] ?? 0,
                'category_id' => $category ? $category->id : null,
                'molos_category_id' => $molosCategoryId,
                'producent_id' => $producent->id,
                'ean' => $productData['ean'] ?? null,
                'status' => ($productData['status_id'] == 1),
                'unit' => $productData['unit'] ?? 'szt.',
                'vat' => $productData['vat_value'] ?? null,
                'slug' => Str::slug($productData['name'], '-', 'en', ['+' => 'plus']),
            ]);

            // Handle main image
            if (!empty($productData['image'])) {
                $this->handleMainImage($product, $productData['image']);
            }

            // Attach gallery images
            if ($gallery) {
                foreach ($gallery as $galleryUrl) {
                    if (!empty($galleryUrl)) {
                        $this->handleGalleryImage($product, $galleryUrl);
                    }
                }
            }

            return $product;

        } catch (\Exception $e) {
            Log::error('Error importing product', [
                'sku' => $productData['symbol'] ?? 'unknown',
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return null;
        }
    }

    /**
     * Update existing product with new data from Molos API
     * Only updates operational data: stock, pricing, and VAT
     */
    public function updateExistingProduct(Product $product, array $productData)
    {
        try {
            Log::info('Updating existing product (operational data only)', [
                'product_id' => $product->id,
                'sku' => $product->sku,
                'molos_id' => $product->molos_id
            ]);

            // Prepare update data - only operational fields
            $updateData = [];
            $hasChanges = false;

            // Update stock
            $newStock = $productData['store'] ?? 0;
            if ($product->stock != $newStock) {
                $updateData['stock'] = $newStock;
                $hasChanges = true;
                Log::info('Stock change detected', [
                    'product_id' => $product->id,
                    'sku' => $product->sku,
                    'old_stock' => $product->stock,
                    'new_stock' => $newStock
                ]);
            }

            // Update Molos price
            $newMolosPrice = $productData['price_gross'] ?? 0;
            if ($product->molos_price != $newMolosPrice) {
                $updateData['molos_price'] = $newMolosPrice;
                $hasChanges = true;
                Log::info('Molos price change detected', [
                    'product_id' => $product->id,
                    'sku' => $product->sku,
                    'old_molos_price' => $product->molos_price,
                    'new_molos_price' => $newMolosPrice
                ]);
            }

            // Update VAT
            $newVat = $productData['vat_value'] ?? null;
            if ($product->vat != $newVat) {
                $updateData['vat'] = $newVat;
                $hasChanges = true;
                Log::info('VAT change detected', [
                    'product_id' => $product->id,
                    'sku' => $product->sku,
                    'old_vat' => $product->vat,
                    'new_vat' => $newVat
                ]);
            }

            // Perform update if there are changes
            if ($hasChanges) {
                $product->update($updateData);

                // Recalculate final price if molos_price was updated
                if (isset($updateData['molos_price'])) {
                    $product->updatePriceFromPercentage();
                    Log::info('Price recalculated after molos_price update', [
                        'product_id' => $product->id,
                        'sku' => $product->sku,
                        'new_price' => $product->fresh()->price
                    ]);
                }

                Log::info('Product updated successfully', [
                    'product_id' => $product->id,
                    'sku' => $product->sku,
                    'updated_fields' => array_keys($updateData)
                ]);
            } else {
                Log::info('No changes needed for product', [
                    'product_id' => $product->id,
                    'sku' => $product->sku
                ]);
            }

            return $product;

        } catch (\Exception $e) {
            Log::error('Error updating product', [
                'product_id' => $product->id,
                'sku' => $product->sku,
                'molos_id' => $product->molos_id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return null;
        }
    }

    public function handleGallery($galleryUrls)
    {
        return array_map([$this, 'encodeUrl'], $galleryUrls);
    }

    public function handleMainImage($product, $imageUrl)
    {
        $encodedUrl = $this->encodeUrl($imageUrl);
        $product->clearMediaCollection('images');
        $product->addMediaFromUrl($encodedUrl)
            ->withResponsiveImages()
            ->toMediaCollection('images');
    }

    public function handleGalleryImage($product, $imageUrl)
    {
        $encodedUrl = $this->encodeUrl($imageUrl);
        $product->addMediaFromUrl($encodedUrl)
            ->withResponsiveImages()
            ->toMediaCollection('gallery');
    }

    public function generateDescription($productName)
    {
        $apiKey = config('services.openai.api_key');
        $response = Http::withHeaders([
            'Authorization' => 'Bearer ' . $apiKey,
            'Content-Type' => 'application/json',
        ])->post('https://api.openai.com/v1/chat/completions', [
            'model' => 'gpt-4o-mini',
            'messages' => [
                [
                    'role' => 'system',
                    'content' => 'You are a helpful assistant.'
                ],
                [
                    'role' => 'user',
                    'content' => "Rewrite this description: $productName To make it SEO friendly and unique."
                    // 'content' => "Generate a SEO product description for the following product: $productName with html tags. It should include full describe the product. Also content if it possible. Sizes table if applicable (also include other sizes, not only for this product). Some additional info. Don't include your system tags. only generated text with html tags. Don't include h1 as title. It should be in Polish"
                ]
            ],
            // 'max_tokens' => 550,
        ]);

        if ($response->successful()) {
            $result = $response->json();
            return $result['choices'][0]['message']['content'] ?? '';
        }

        return null;
    }

    /**
     * Get basic product data (stock, prices) for partial updates
     */
    public function getBasicProducts($page = 1, $limit = 100)
    {
        $response = $this->getHttpClient()->get($this->apiUrl . '/products/basic', [
            'page' => $page,
            'limit' => $limit,
            'status_id' => 4, // Only active products
        ]);

        if ($response->successful()) {
            $result = $response->json();
            $products = $result['products'] ?? [];
            $total = $result['total'] ?? 0;
            $next = ($page * $limit) < $total;

            return [$products, $next];
        } else {
            Log::error('Failed to fetch basic products from Molos API', [
                'page' => $page, 
                'limit' => $limit,
                'response' => $response->body()
            ]);
            return [[], false];
        }
    }

    /**
     * Get store/stock data specifically
     */
    public function getStoreData($page = 1, $limit = 100)
    {
        $response = $this->getHttpClient()->get($this->apiUrl . '/products/store', [
            'page' => $page,
            'limit' => $limit,
        ]);

        if ($response->successful()) {
            $result = $response->json();
            $products = $result['products'] ?? [];
            $total = $result['total'] ?? 0;
            $next = ($page * $limit) < $total;

            return [$products, $next];
        } else {
            Log::error('Failed to fetch store data from Molos API', [
                'page' => $page, 
                'limit' => $limit,
                'response' => $response->body()
            ]);
            return [[], false];
        }
    }

    /**
     * Update stock and prices for existing products
     */
    public function updateStockAndPrices($page = 1, $limit = 100)
    {
        list($products, $next) = $this->getBasicProducts($page, $limit);

        if (empty($products)) {
            Log::info('No products to update on page ' . $page);
            return false;
        }

        $updatedCount = 0;
        $skippedCount = 0;

        foreach ($products as $productData) {
            try {
                $updated = $this->updateSingleProductStockAndPrice($productData);
                if ($updated) {
                    $updatedCount++;
                } else {
                    $skippedCount++;
                }
            } catch (\Exception $e) {
                Log::error('Failed to update product stock/price', [
                    'sku' => $productData['symbol'] ?? 'unknown',
                    'error' => $e->getMessage()
                ]);
                $skippedCount++;
                continue;
            }
        }

        Log::info('Stock and price update completed for page ' . $page, [
            'updated' => $updatedCount,
            'skipped' => $skippedCount,
            'total_processed' => count($products)
        ]);

        return $next;
    }

    /**
     * Update stock and price for a single product
     */
    protected function updateSingleProductStockAndPrice($productData)
    {
        $molosId = $productData['id'] ?? null;
        $sku = $productData['symbol'] ?? null;
        
        if (empty($molosId) && empty($sku)) {
            Log::warning('Product data missing both molos_id and SKU', ['data' => $productData]);
            return false;
        }

        // Find existing product - prefer molos_id, fallback to SKU
        $product = null;
        if (!empty($molosId)) {
            $product = Product::where('molos_id', $molosId)->first();
        }
        if (!$product && !empty($sku)) {
            $product = Product::where('sku', $sku)->first();
            
            // If found by SKU but missing molos_id, update it
            if ($product && !empty($molosId) && empty($product->molos_id)) {
                $product->update(['molos_id' => $molosId]);
                Log::info('Updated missing molos_id for product', ['sku' => $sku, 'molos_id' => $molosId]);
            }
        }
        
        if (!$product) {
            Log::info('Product not found in database, skipping update', [
                'molos_id' => $molosId,
                'sku' => $sku
            ]);
            return false;
        }

        // Prepare update data
        $updateData = [];
        $hasChanges = false;

        // Update stock
        $newStock = $productData['store'] ?? $productData['stock'] ?? 0;
        if ($product->stock != $newStock) {
            $updateData['stock'] = $newStock;
            $hasChanges = true;
            Log::info('Stock change detected', [
                'molos_id' => $molosId,
                'sku' => $sku,
                'old_stock' => $product->stock,
                'new_stock' => $newStock
            ]);
        }

        // Update Molos price
        $newMolosPrice = $productData['price_gross'] ?? $productData['price'] ?? 0;
        if ($product->molos_price != $newMolosPrice) {
            $updateData['molos_price'] = $newMolosPrice;
            $hasChanges = true;

            Log::info('Price change detected', [
                'molos_id' => $molosId,
                'sku' => $sku,
                'old_molos_price' => $product->molos_price,
                'new_molos_price' => $newMolosPrice
            ]);
        }

        // Update product status if provided
        if (isset($productData['status_id'])) {
            $newStatus = $productData['status_id'] == 4; // Active status
            if ($product->status != $newStatus) {
                $updateData['status'] = $newStatus;
                $hasChanges = true;
                Log::info('Status change detected', [
                    'molos_id' => $molosId,
                    'sku' => $sku,
                    'old_status' => $product->status,
                    'new_status' => $newStatus
                ]);
            }
        }

        // Perform update if there are changes
        if ($hasChanges) {
            $product->update($updateData);

            // Recalculate final price if molos_price was updated
            if (isset($updateData['molos_price'])) {
                $product->updatePriceFromPercentage();
                Log::info('Price recalculated after molos_price update', [
                    'molos_id' => $molosId,
                    'sku' => $sku,
                    'new_price' => $product->fresh()->price
                ]);
            }

            Log::info('Product updated successfully', [
                'molos_id' => $molosId,
                'sku' => $sku,
                'updated_fields' => array_keys($updateData)
            ]);
            return true;
        }

        return false; // No changes needed
    }

    /**
     * Update only stock for existing products (faster operation)
     */
    public function updateStockOnly($page = 1, $limit = 100)
    {
        list($products, $next) = $this->getStoreData($page, $limit);

        if (empty($products)) {
            Log::info('No products to update stock on page ' . $page);
            return false;
        }

        $updatedCount = 0;
        $skippedCount = 0;

        foreach ($products as $productData) {
            try {
                $molosId = $productData['id'] ?? null;
                $sku = $productData['symbol'] ?? null;
                
                if (empty($molosId) && empty($sku)) {
                    $skippedCount++;
                    continue;
                }

                // Find existing product - prefer molos_id, fallback to SKU
                $product = null;
                if (!empty($molosId)) {
                    $product = Product::where('molos_id', $molosId)->first();
                }
                if (!$product && !empty($sku)) {
                    $product = Product::where('sku', $sku)->first();
                    
                    // If found by SKU but missing molos_id, update it
                    if ($product && !empty($molosId) && empty($product->molos_id)) {
                        $product->update(['molos_id' => $molosId]);
                        Log::info('Updated missing molos_id for product', ['sku' => $sku, 'molos_id' => $molosId]);
                    }
                }
                
                if (!$product) {
                    $skippedCount++;
                    continue;
                }

                $newStock = $productData['store'] ?? 0;
                if ($product->stock != $newStock) {
                    $product->update(['stock' => $newStock]);
                    $updatedCount++;
                    
                    Log::info('Stock updated', [
                        'molos_id' => $molosId,
                        'sku' => $sku,
                        'old_stock' => $product->stock,
                        'new_stock' => $newStock
                    ]);
                } else {
                    $skippedCount++;
                }
            } catch (\Exception $e) {
                Log::error('Failed to update product stock', [
                    'molos_id' => $productData['id'] ?? 'unknown',
                    'sku' => $productData['symbol'] ?? 'unknown',
                    'error' => $e->getMessage()
                ]);
                $skippedCount++;
            }
        }

        Log::info('Stock update completed for page ' . $page, [
            'updated' => $updatedCount,
            'skipped' => $skippedCount,
            'total_processed' => count($products)
        ]);

        return $next;
    }

    /**
     * Generate product dimensions using OpenAI
     */
    public function generateProductDimensions(Product $product)
    {
        $apiKey = config('services.openai.api_key');
        
        if (empty($apiKey)) {
            Log::warning('OpenAI API key not configured for dimension generation');
            return null;
        }

        $productInfo = $product->name;
        if ($product->description) {
            $productInfo .= '. ' . strip_tags($product->description);
        }
        if ($product->category) {
            $productInfo .= '. Category: ' . $product->category->name;
        }

        $prompt = "Based on the product information, estimate realistic dimensions (length, width, height in centimeters) and weight (in kilograms) for shipping purposes.

Product: {$productInfo}

Please provide ONLY a JSON response in this exact format:
{
    \"length\": 15.5,
    \"width\": 10.2,
    \"height\": 8.0,
    \"weight\": 0.5,
    \"reasoning\": \"Brief explanation of the estimate\"
}

Consider:
- Typical product dimensions for this category
- Packaging requirements
- Shipping constraints
- Be conservative (slightly larger than actual product for packaging)
- Length, width, height should be practical for shipping
- Weight should include packaging";

        try {
            $response = Http::timeout(30)->withHeaders([
                'Authorization' => 'Bearer ' . $apiKey,
                'Content-Type' => 'application/json',
            ])->post('https://api.openai.com/v1/chat/completions', [
                'model' => 'gpt-4o-mini',
                'messages' => [
                    [
                        'role' => 'system',
                        'content' => 'You are an expert in product packaging and shipping. Always respond with valid JSON only.'
                    ],
                    [
                        'role' => 'user',
                        'content' => $prompt
                    ]
                ],
                'max_tokens' => 200,
                'temperature' => 0.3, // Lower temperature for more consistent results
            ]);

            if ($response->successful()) {
                $result = $response->json();
                $content = $result['choices'][0]['message']['content'] ?? '';
                
                // Clean the response and extract JSON
                $content = trim($content);
                $content = preg_replace('/```json\s*/', '', $content);
                $content = preg_replace('/```\s*$/', '', $content);
                
                $dimensions = json_decode($content, true);
                
                if ($dimensions && isset($dimensions['length'], $dimensions['width'], $dimensions['height'], $dimensions['weight'])) {
                    // Validate dimensions are reasonable
                    if ($dimensions['length'] > 0 && $dimensions['length'] <= 200 &&
                        $dimensions['width'] > 0 && $dimensions['width'] <= 200 &&
                        $dimensions['height'] > 0 && $dimensions['height'] <= 200 &&
                        $dimensions['weight'] > 0 && $dimensions['weight'] <= 100) {
                        
                        // Update the product
                        $product->update([
                            'length' => round($dimensions['length'], 2),
                            'width' => round($dimensions['width'], 2),
                            'height' => round($dimensions['height'], 2),
                            'weight' => round($dimensions['weight'], 3),
                        ]);

                        Log::info('Generated dimensions for product', [
                            'product_id' => $product->id,
                            'sku' => $product->sku,
                            'dimensions' => $dimensions,
                        ]);

                        return $dimensions;
                    } else {
                        Log::warning('Generated dimensions are out of reasonable bounds', [
                            'product_id' => $product->id,
                            'dimensions' => $dimensions
                        ]);
                    }
                } else {
                    Log::warning('Invalid dimensions format from OpenAI', [
                        'product_id' => $product->id,
                        'response' => $content
                    ]);
                }
            } else {
                Log::error('OpenAI API request failed for dimension generation', [
                    'product_id' => $product->id,
                    'status' => $response->status(),
                    'body' => $response->body()
                ]);
            }
        } catch (\Exception $e) {
            Log::error('Exception during dimension generation', [
                'product_id' => $product->id,
                'error' => $e->getMessage()
            ]);
        }

        return null;
    }

    /**
     * Ensure product has dimensions, generate if missing
     */
    public function ensureProductDimensions(Product $product)
    {
        if ($product->hasReasonableDimensions()) {
            return $product->getDimensions();
        }

        Log::info('Product missing dimensions, generating with AI', [
            'product_id' => $product->id,
            'sku' => $product->sku
        ]);

        $generated = $this->generateProductDimensions($product);
        
        if ($generated) {
            return $generated;
        }

        // Fallback to default dimensions if AI generation fails
        $defaultDimensions = [
            'length' => 20,
            'width' => 15,
            'height' => 10,
            'weight' => 0.5,
        ];

        $product->update($defaultDimensions);
        
        Log::warning('Used default dimensions for product', [
            'product_id' => $product->id,
            'sku' => $product->sku,
            'dimensions' => $defaultDimensions
        ]);

        return $defaultDimensions;
    }
}
