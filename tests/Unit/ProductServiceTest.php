<?php

namespace Tests\Unit;

use Tests\TestCase;
use App\Services\ProductService;
use App\Models\Product;
use App\Models\Category;
use Illuminate\Foundation\Testing\RefreshDatabase;

class ProductServiceTest extends TestCase
{
    use RefreshDatabase;

    protected $productService;

    protected function setUp(): void
    {
        parent::setUp();
        $this->productService = new ProductService();
    }

    /** @test */
    public function it_extracts_base_name_correctly()
    {
        $reflection = new \ReflectionClass($this->productService);
        $method = $reflection->getMethod('extractBaseName');
        $method->setAccessible(true);

        // Test weight removal
        $this->assertEquals(
            'Acana Adult Dog',
            $method->invoke($this->productService, 'Acana Adult Dog 17kg')
        );

        $this->assertEquals(
            'Acana Adult Dog',
            $method->invoke($this->productService, 'Acana Adult Dog 11,4kg')
        );

        // Test size and color removal
        $this->assertEquals(
            'CHABA Szelki Guard Comfort Classic',
            $method->invoke($this->productService, 'CHABA Szelki Guard Comfort Classic M czarne')
        );

        $this->assertEquals(
            'CHABA Szelki Guard Comfort Classic',
            $method->invoke($this->productService, 'CHABA Szelki Guard Comfort Classic S czerwone')
        );

        // Test no changes needed
        $this->assertEquals(
            'Simple Product Name',
            $method->invoke($this->productService, 'Simple Product Name')
        );
    }

    /** @test */
    public function it_checks_base_name_similarity_correctly()
    {
        $reflection = new \ReflectionClass($this->productService);
        $method = $reflection->getMethod('areBaseNamesSimilar');
        $method->setAccessible(true);

        // Exact match
        $this->assertTrue(
            $method->invoke($this->productService, 'Acana Adult Dog', 'Acana Adult Dog')
        );

        // Case insensitive match
        $this->assertTrue(
            $method->invoke($this->productService, 'acana adult dog', 'ACANA ADULT DOG')
        );

        // High similarity
        $this->assertTrue(
            $method->invoke($this->productService, 'CHABA Szelki Guard Comfort Classic', 'CHABA Szelki Guard Comfort')
        );

        // Low similarity
        $this->assertFalse(
            $method->invoke($this->productService, 'Completely Different Product', 'Another Product Name')
        );
    }

    /** @test */
    public function it_extracts_variant_differences_correctly()
    {
        // Test weight difference
        $this->assertEquals(
            '17kg',
            $this->productService->extractVariantDifference('Acana Adult Dog 17kg', 'Acana Adult Dog')
        );

        // Test size and color difference
        $this->assertEquals(
            'M czarne',
            $this->productService->extractVariantDifference('CHABA Szelki Guard Comfort Classic M czarne', 'CHABA Szelki Guard Comfort Classic')
        );

        // Test flavor and weight combinations (Polish cat food example)
        $this->assertEquals(
            'Tuńczyk i Ser 70g',
            $this->productService->extractVariantDifference('Applaws puszka dla kota Tuńczyk i Ser 70g', 'Applaws puszka dla kota')
        );

        $this->assertEquals(
            'Tuńczyk 156g',
            $this->productService->extractVariantDifference('Applaws puszka dla kota Tuńczyk 156g', 'Applaws puszka dla kota')
        );

        $this->assertEquals(
            'Tuńczyk i Wodorosty 70g',
            $this->productService->extractVariantDifference('Applaws puszka dla kota Tuńczyk i Wodorosty 70g', 'Applaws puszka dla kota')
        );

        // Test when no clear difference
        $this->assertEquals(
            'Standard',
            $this->productService->extractVariantDifference('Simple Product', 'Simple Product')
        );
    }

    /** @test */
    public function it_finds_similar_products_in_same_category()
    {
        // Create a category
        $category = Category::factory()->create();

        // Create main product
        $mainProduct = Product::factory()->create([
            'name' => 'Acana Adult Dog 17kg',
            'category_id' => $category->id,
            'stock' => 10
        ]);

        // Create similar products
        $similarProduct1 = Product::factory()->create([
            'name' => 'Acana Adult Dog 11,4kg',
            'category_id' => $category->id,
            'stock' => 5
        ]);

        $similarProduct2 = Product::factory()->create([
            'name' => 'Acana Adult Dog 2kg',
            'category_id' => $category->id,
            'stock' => 8
        ]);

        // Create different product (should not be included)
        $differentProduct = Product::factory()->create([
            'name' => 'Royal Canin Adult Cat 2kg',
            'category_id' => $category->id,
            'stock' => 3
        ]);

        // Create product in different category (should not be included)
        $differentCategory = Category::factory()->create();
        $productInDifferentCategory = Product::factory()->create([
            'name' => 'Acana Adult Dog 5kg',
            'category_id' => $differentCategory->id,
            'stock' => 7
        ]);

        $similarProducts = $this->productService->findSimilarProducts($mainProduct);

        $this->assertCount(2, $similarProducts);
        $this->assertTrue($similarProducts->contains($similarProduct1));
        $this->assertTrue($similarProducts->contains($similarProduct2));
        $this->assertFalse($similarProducts->contains($differentProduct));
        $this->assertFalse($similarProducts->contains($productInDifferentCategory));
        $this->assertFalse($similarProducts->contains($mainProduct));
    }

    /** @test */
    public function it_excludes_out_of_stock_products()
    {
        $category = Category::factory()->create();

        $mainProduct = Product::factory()->create([
            'name' => 'Test Product 1kg',
            'category_id' => $category->id,
            'stock' => 10
        ]);

        // Out of stock similar product
        $outOfStockProduct = Product::factory()->create([
            'name' => 'Test Product 2kg',
            'category_id' => $category->id,
            'stock' => 0
        ]);

        $similarProducts = $this->productService->findSimilarProducts($mainProduct);

        $this->assertCount(0, $similarProducts);
        $this->assertFalse($similarProducts->contains($outOfStockProduct));
    }

    /** @test */
    public function it_returns_empty_collection_when_no_category()
    {
        $product = Product::factory()->create([
            'category_id' => null
        ]);

        $similarProducts = $this->productService->findSimilarProducts($product);

        $this->assertTrue($similarProducts->isEmpty());
    }

    /** @test */
    public function it_finds_optimal_base_name_for_applaws_products()
    {
        $reflection = new \ReflectionClass($this->productService);
        $method = $reflection->getMethod('findOptimalBaseName');
        $method->setAccessible(true);

        $productNames = [
            'Applaws puszka dla kota Tuńczyk i Ser 70g',
            'Applaws puszka dla kota Tuńczyk 156g',
            'Applaws puszka dla kota Tuńczyk 70g',
            'Applaws puszka dla kota Tuńczyk i Wodorosty 70g'
        ];

        $baseName = $method->invoke($this->productService, $productNames);

        // Should extract the common part: "Applaws puszka dla kota"
        $this->assertEquals('Applaws puszka dla kota', $baseName);
    }

    /** @test */
    public function it_handles_variant_differences_for_complete_applaws_example()
    {
        $category = Category::factory()->create();

        // Create the main product
        $mainProduct = Product::factory()->create([
            'name' => 'Applaws puszka dla kota Tuńczyk i Ser 70g',
            'category_id' => $category->id,
            'stock' => 10
        ]);

        // Create variant products
        $variant1 = Product::factory()->create([
            'name' => 'Applaws puszka dla kota Tuńczyk 156g',
            'category_id' => $category->id,
            'stock' => 5
        ]);

        $variant2 = Product::factory()->create([
            'name' => 'Applaws puszka dla kota Tuńczyk 70g',
            'category_id' => $category->id,
            'stock' => 8
        ]);

        $variant3 = Product::factory()->create([
            'name' => 'Applaws puszka dla kota Tuńczyk i Wodorosty 70g',
            'category_id' => $category->id,
            'stock' => 3
        ]);

        $similarProducts = $this->productService->findSimilarProducts($mainProduct);
        $variants = $this->productService->getVariantDifferences($mainProduct, $similarProducts);

        // Should find 3 similar products
        $this->assertCount(3, $similarProducts);

        // Should have 4 total variants (main + 3 similar)
        $this->assertCount(4, $variants);

        // Check that the main product is marked as current
        $currentVariant = collect($variants)->firstWhere('is_current', true);
        $this->assertNotNull($currentVariant);
        $this->assertEquals($mainProduct->id, $currentVariant['product']->id);

        // Check that variant differences are meaningful
        $differences = collect($variants)->pluck('difference')->toArray();

        // Should contain flavor and weight information
        $this->assertContains('Tuńczyk i Ser 70g', $differences);
        $this->assertContains('Tuńczyk 156g', $differences);
        $this->assertContains('Tuńczyk 70g', $differences);
        $this->assertContains('Tuńczyk i Wodorosty 70g', $differences);
    }
}
